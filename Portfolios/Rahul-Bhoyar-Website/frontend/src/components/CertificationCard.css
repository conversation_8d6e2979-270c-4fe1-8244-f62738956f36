.certification-card {
  border: none;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.4s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  background-color: rgba(30, 30, 30, 0.6);
  position: relative;
  z-index: 1;
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
  height: 100%;
}

.certification-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(187, 134, 252, 0.05), rgba(3, 218, 198, 0.05));
  z-index: -1;
}

.certification-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--md-purple), var(--md-teal));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
  z-index: 1;
}

.certification-card:hover {
  transform: translateY(-12px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(187, 134, 252, 0.3);
}

.certification-card:hover::after {
  transform: scaleX(1);
}

.cert-header {
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(30, 30, 30, 0.9), rgba(18, 18, 18, 0.9));
}

.cert-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at center, rgba(187, 134, 252, 0.3) 0%, transparent 70%),
    radial-gradient(circle at 30% 70%, rgba(3, 218, 198, 0.2) 0%, transparent 60%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.certification-card:hover .cert-header::before {
  opacity: 1;
}

.cert-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.cert-icon {
  font-size: 4rem;
  filter: drop-shadow(0 4px 10px rgba(0, 0, 0, 0.3));
  transition: all 0.4s ease;
  color: rgba(255, 255, 255, 0.9);
}

.certification-card:hover .cert-icon {
  transform: scale(1.1) rotate(5deg);
  color: white;
  filter: drop-shadow(0 0 15px rgba(187, 134, 252, 0.5));
}

/* Certificate image styles */
.cert-image {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
}

.cert-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  padding: 1rem;
  z-index: 2;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
}

.cert-category-badge {
  background: linear-gradient(90deg, rgba(187, 134, 252, 0.9), rgba(3, 218, 198, 0.9));
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.certification-card:hover .cert-category-badge {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.certification-card .card-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
  position: relative;
  display: inline-block;
}

.certification-card .card-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--md-purple), var(--md-teal));
  transition: width 0.3s ease;
}

.certification-card:hover .card-title::after {
  width: 100%;
}

.cert-issuer {
  color: var(--text-secondary);
  font-size: 0.95rem;
  font-weight: 500;
}

.cert-date {
  color: var(--text-tertiary);
  font-size: 0.9rem;
}

.expiry-date {
  color: var(--md-teal);
  font-weight: 500;
}

.certification-card .card-text {
  color: var(--text-secondary);
  font-size: 0.95rem;
  margin-bottom: 1.25rem;
  line-height: 1.6;
}

.certification-card .card-body {
  padding: 1.5rem;
  text-align: left;
}

.skill-badge {
  background-color: rgba(30, 30, 30, 0.8) !important;
  color: var(--md-teal) !important;
  font-weight: 500;
  padding: 0.35rem 0.75rem;
  border-radius: 50px;
  transition: all 0.3s ease;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  position: relative;
  overflow: hidden;
  z-index: 1;
  border: 1px solid rgba(3, 218, 198, 0.2);
}

.skill-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(187, 134, 252, 0.8), rgba(3, 218, 198, 0.8));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.skill-badge:hover {
  color: white !important;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(3, 218, 198, 0.3);
  border-color: transparent;
}

.skill-badge:hover::before {
  opacity: 1;
}

.certification-card .card-footer {
  background-color: rgba(18, 18, 18, 0.4);
  border-top: 1px solid rgba(187, 134, 252, 0.1);
  padding: 1rem 1.25rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.certification-card .btn {
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  font-weight: 500;
  font-size: 0.9rem;
  border-radius: 8px;
}

.certification-card .btn:hover {
  transform: translateY(-2px);
}

.certification-card .btn-primary {
  background: linear-gradient(90deg, var(--md-teal), var(--md-teal-variant));
  border: none;
  position: relative;
  overflow: hidden;
  color: white;
  box-shadow: 0 4px 10px rgba(3, 218, 198, 0.2);
}

.certification-card .btn-primary:hover {
  background: linear-gradient(90deg, var(--md-teal-variant), var(--md-teal));
  box-shadow: 0 6px 15px rgba(3, 218, 198, 0.3);
}

.credential-id {
  color: var(--text-tertiary);
  font-size: 0.8rem;
  margin-top: 0.5rem;
}
