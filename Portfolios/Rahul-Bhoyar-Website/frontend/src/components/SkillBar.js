import React from 'react';
import { ProgressBar } from 'react-bootstrap';

// Helper function to get technology logo for skill bars
const getSkillLogo = (skillName) => {
  const skillLogos = {
    // Programming Languages
    'Python': 'python.png',
    'JavaScript': 'JavaScript.png',
    'TypeScript': 'JavaScript.png',
    'Java': 'default-tech.svg',
    'C++': 'default-tech.svg',
    'SQL': 'default-tech.svg',

    // AI/ML Frameworks
    'TensorFlow': 'Tensorflow_logo.png',
    'PyTorch': 'pytorch_logo.png',
    'Scikit-learn': 'default-tech.svg',
    'Keras': 'Tensorflow_logo.png',
    'OpenCV': 'default-tech.svg',
    'NLTK': 'default-tech.svg',
    'spaCy': 'default-tech.svg',
    'Pandas': 'default-tech.svg',
    'NumPy': 'default-tech.svg',
    'Matplotlib': 'default-tech.svg',

    // Web Frameworks
    'React': 'React.png',
    'React.js': 'React.png',
    'Django': 'Django.png',
    'Flask': 'flask.png',
    'FastAPI': 'FastAPI_logo.png',
    'Node.js': 'default-tech.svg',
    'Express.js': 'default-tech.svg',

    // Databases
    'PostgreSQL': 'default-tech.svg',
    'MySQL': 'default-tech.svg',
    'MongoDB': 'default-tech.svg',
    'SQLite': 'default-tech.svg',
    'Redis': 'default-tech.svg',

    // Cloud & DevOps
    'AWS': 'default-tech.svg',
    'Azure': 'default-tech.svg',
    'GCP': 'default-tech.svg',
    'Docker': 'default-tech.svg',
    'Kubernetes': 'default-tech.svg',
    'Git': 'default-tech.svg',

    // AI/ML Specific
    'Machine Learning': 'default-tech.svg',
    'Deep Learning': 'Tensorflow_logo.png',
    'Natural Language Processing': 'default-tech.svg',
    'Computer Vision': 'default-tech.svg',
    'Data Science': 'default-tech.svg',
    'MLOps': 'default-tech.svg',

    // Generative AI
    'LangChain': 'Langchain.png',
    'LangGraph': 'Langgraph.png',
    'OpenAI': 'default-tech.svg',
    'Hugging Face': 'Huggingface_logo.svg',
    'LlamaIndex': 'default-tech.svg',

    // Tools
    'Jupyter': 'Jupyter_logo.png',
    'VS Code': 'default-tech.svg',
    'Postman': 'default-tech.svg',
    'Tableau': 'default-tech.svg'
  };

  // Check for exact match first
  if (skillLogos[skillName]) {
    return `/images/about/skills/${skillLogos[skillName]}`;
  }

  // Check for partial matches
  const lowerSkillName = skillName.toLowerCase();
  for (const [key, value] of Object.entries(skillLogos)) {
    if (lowerSkillName.includes(key.toLowerCase()) || key.toLowerCase().includes(lowerSkillName)) {
      return `/images/about/skills/${value}`;
    }
  }

  // Default icon for unknown skills
  return `/images/about/skills/default-tech.svg`;
};

const SkillBar = ({ skill }) => {
  const logoSrc = getSkillLogo(skill.name);

  return (
    <div className="mb-4 skill-bar-container">
      <div className="d-flex align-items-center mb-2">
        <img
          src={`${process.env.PUBLIC_URL}${logoSrc}`}
          alt={`${skill.name} logo`}
          className="skill-bar-logo"
          onError={(e) => {
            e.target.src = `${process.env.PUBLIC_URL}/images/about/skills/default-tech.svg`;
          }}
        />
        <div className="skill-info-container flex-grow-1">
          <div className="d-flex justify-content-between align-items-center mb-1">
            <span className="skill-name">{skill.name}</span>
            <span className="skill-percentage">{skill.level}%</span>
          </div>
          <ProgressBar
            now={skill.level}
            variant={getVariantByLevel(skill.level)}
            className="skill-bar"
          />
        </div>
      </div>
    </div>
  );
};

// Helper function to determine the variant based on skill level
const getVariantByLevel = (level) => {
  if (level >= 80) return 'success';
  if (level >= 60) return 'info';
  if (level >= 40) return 'warning';
  return 'danger';
};

export default SkillBar;
