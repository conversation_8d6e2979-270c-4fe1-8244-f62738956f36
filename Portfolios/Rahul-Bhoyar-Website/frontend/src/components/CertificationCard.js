import React from 'react';
import { <PERSON>, <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';
import {
  FaCertificate, FaAward, FaGraduationCap, FaCode, FaCloud, 
  FaDatabase, FaShieldAlt, FaRobot, FaExternalLinkAlt
} from 'react-icons/fa';
import './CertificationCard.css';

const CertificationCard = ({ certification }) => {
  // Function to get an icon based on the certification category or issuer
  const getCertificationIcon = (title, issuer, category) => {
    const titleLower = title.toLowerCase();
    const issuerLower = issuer.toLowerCase();
    const categoryLower = category ? category.toLowerCase() : '';

    if (titleLower.includes('aws') || issuerLower.includes('amazon')) {
      return <FaCloud className="cert-icon" />;
    } else if (titleLower.includes('azure') || issuerLower.includes('microsoft')) {
      return <FaCloud className="cert-icon" />;
    } else if (titleLower.includes('gcp') || titleLower.includes('google')) {
      return <FaCloud className="cert-icon" />;
    } else if (titleLower.includes('security') || titleLower.includes('cybersecurity')) {
      return <FaShieldAlt className="cert-icon" />;
    } else if (titleLower.includes('ai') || titleLower.includes('machine learning') || titleLower.includes('data science')) {
      return <FaRobot className="cert-icon" />;
    } else if (titleLower.includes('database') || titleLower.includes('sql')) {
      return <FaDatabase className="cert-icon" />;
    } else if (titleLower.includes('developer') || titleLower.includes('programming')) {
      return <FaCode className="cert-icon" />;
    } else if (categoryLower.includes('education') || issuerLower.includes('university')) {
      return <FaGraduationCap className="cert-icon" />;
    } else if (titleLower.includes('professional') || titleLower.includes('expert')) {
      return <FaAward className="cert-icon" />;
    } else {
      return <FaCertificate className="cert-icon" />;
    }
  };

  return (
    <Card className="h-100 certification-card">
      <div className="cert-header">
        {certification.image_url ? (
          <div 
            className="cert-image"
            style={{
              backgroundImage: `url(${certification.image_url})`,
              backgroundSize: 'contain',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat'
            }}
          >
            <div className="cert-overlay">
              <div className="cert-category-badge">
                {certification.category || 'Professional'}
              </div>
            </div>
          </div>
        ) : (
          <div className="cert-icon-container">
            {getCertificationIcon(certification.title, certification.issuer, certification.category)}
          </div>
        )}
      </div>
      <Card.Body>
        <Card.Title>{certification.title}</Card.Title>
        <div className="cert-issuer mb-2">
          <strong>Issued by:</strong> {certification.issuer}
        </div>
        <div className="cert-date mb-3">
          <strong>Date:</strong> {certification.date}
          {certification.expiry_date && (
            <span className="expiry-date"> • Expires: {certification.expiry_date}</span>
          )}
        </div>
        {certification.description && (
          <Card.Text>{certification.description}</Card.Text>
        )}
        {certification.skills && certification.skills.length > 0 && (
          <div className="mb-3">
            {certification.skills.map((skill, index) => (
              <Badge bg="secondary" className="skill-badge me-2 mb-2" key={index}>
                {skill}
              </Badge>
            ))}
          </div>
        )}
      </Card.Body>
      <Card.Footer>
        {certification.verification_url ? (
          <Button
            variant="primary"
            href={certification.verification_url}
            target="_blank"
            rel="noopener noreferrer"
            size="sm"
          >
            <FaExternalLinkAlt className="me-2" /> Verify Credential
          </Button>
        ) : (
          <Button variant="outline-secondary" size="sm" disabled>
            Verification Unavailable
          </Button>
        )}
        {certification.credential_id && (
          <div className="credential-id mt-2">
            <small>ID: {certification.credential_id}</small>
          </div>
        )}
      </Card.Footer>
    </Card>
  );
};

export default CertificationCard;
