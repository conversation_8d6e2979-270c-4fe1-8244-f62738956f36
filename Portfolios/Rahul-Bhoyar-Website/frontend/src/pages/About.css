/* About Content */
.about-content {
  padding-right: 2rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.text-gradient {
  background: linear-gradient(135deg, var(--md-purple), var(--md-teal));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

/* About Stats */
.about-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.stat-item {
  text-align: center;
  padding: 1.5rem;
  background-color: rgba(30, 30, 30, 0.6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  flex: 1;
  margin: 0 0.5rem;
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
}

.stat-item:first-child {
  margin-left: 0;
}

.stat-item:last-child {
  margin-right: 0;
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(187, 134, 252, 0.3);
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--md-purple), var(--md-teal));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* About Card */
.about-card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: all 0.3s ease;
}

.about-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.about-card-header {
  background: var(--gradient-primary);
  padding: 2rem;
  display: flex;
  align-items: center;
  color: white;
}

.about-card-avatar-container {
  margin-right: 1.5rem;
  position: relative;
}

.about-card-avatar-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid white;
  transition: all 0.3s ease;
}

.about-card-avatar-image:hover {
  transform: scale(1.05);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.about-card-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  border: 3px solid white;
}

.about-card-info h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.about-card-info p {
  margin: 0.5rem 0 0;
  opacity: 0.9;
  font-size: 1rem;
}

.about-card-body {
  padding: 1.5rem;
}

.about-card-section {
  margin-bottom: 1.5rem;
}

.about-card-section:last-child {
  margin-bottom: 0;
}

.about-card-section h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
}

.about-card-section p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.95rem;
}

.tech-stack-icons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.tech-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--bg-secondary);
  color: var(--primary-500);
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.tech-icon:hover {
  background-color: var(--md-purple);
  color: white;
  transform: rotate(10deg) scale(1.1);
  box-shadow: 0 0 15px rgba(187, 134, 252, 0.5);
}

/* Skills Section */
.skills-intro {
  max-width: 700px;
  margin: 0 auto;
}

/* Reduce gap before Technical Skills section */
.section.bg-gradient-dark.reduced-gap {
  margin-top: 1rem !important;
  padding-top: 2rem !important;
}

.skill-category-card {
  background-color: rgba(30, 30, 30, 0.6);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  height: 100%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.skill-category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(187, 134, 252, 0.3);
}

.skill-list {
  list-style-type: none;
  padding-left: 0;
  margin-bottom: 0;
}

.skill-list li {
  padding: 0.5rem 0;
  position: relative;
  padding-left: 1.5rem;
  color: var(--text-secondary);
}

.skill-list li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--md-purple);
  font-size: 1.2rem;
}

/* Skill list item with logo styles */
.skill-list-item-with-logo {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  position: relative;
  padding-left: 0 !important;
  color: var(--text-secondary);
}

.skill-list-item-with-logo::before {
  display: none !important;
}

.skill-logo {
  width: 20px;
  height: 20px;
  margin-right: 0.75rem;
  object-fit: contain;
  flex-shrink: 0;
  border-radius: 3px;
  transition: all 0.3s ease;
}

.skill-list-item-with-logo:hover .skill-logo {
  transform: scale(1.1);
  filter: brightness(1.2);
}

/* Skill Bar Logo Styles */
.skill-bar-logo {
  width: 40px;
  height: 40px;
  margin-right: 1rem;
  object-fit: contain;
  flex-shrink: 0;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.skill-bar-logo:hover {
  transform: scale(1.15);
  filter: brightness(1.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.skill-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
  margin-left: 0.25rem;
}

.skill-percentage {
  font-weight: 700;
  color: var(--text-secondary);
  font-size: 1rem;
  background: linear-gradient(135deg, var(--md-purple), var(--md-teal));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Skill Bar Container Styles */
.skill-bar-container {
  background-color: rgba(30, 30, 30, 0.4);
  border-radius: var(--radius-lg);
  padding: 1rem;
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.skill-bar-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  border-color: rgba(187, 134, 252, 0.3);
  background-color: rgba(30, 30, 30, 0.6);
}

.skill-info-container {
  margin-left: 0.5rem;
}

/* Enhanced Progress Bar Styles */
.skill-bar {
  height: 8px;
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.1) !important;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.skill-bar .progress-bar {
  border-radius: 10px;
  background: linear-gradient(90deg, var(--md-purple), var(--md-teal)) !important;
  box-shadow: 0 2px 8px rgba(187, 134, 252, 0.3);
  transition: all 0.3s ease;
}

.skill-bar-container:hover .skill-bar .progress-bar {
  box-shadow: 0 2px 12px rgba(187, 134, 252, 0.5);
  filter: brightness(1.1);
}

/* Experience Section */
.experience-card {
  background-color: rgba(30, 30, 30, 0.6);
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  padding: 1.5rem;
}

.experience-card:hover {
  transform: translateX(10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(187, 134, 252, 0.3);
}

.experience-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.experience-position {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  color: var(--md-purple);
}

.experience-duration {
  font-size: 0.9rem;
  color: var(--text-tertiary);
  background-color: rgba(187, 134, 252, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
}

.experience-company {
  display: flex;
  align-items: center;
}

.company-logo-container {
  margin-right: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 80px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 10px;
  border: 1px solid rgba(187, 134, 252, 0.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.company-logo {
  height: auto;
  max-height: 70px;
  max-width: 110px;
  object-fit: contain;
  transition: all 0.3s ease;
}

.experience-card:hover .company-logo-container {
  background-color: rgba(187, 134, 252, 0.1);
  border-color: rgba(187, 134, 252, 0.3);
  box-shadow: 0 5px 15px rgba(187, 134, 252, 0.1);
}

.experience-card:hover .company-logo {
  transform: rotate(5deg) scale(1.1);
  box-shadow: 0 0 15px rgba(187, 134, 252, 0.5);
}

.company-logo-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: rgba(187, 134, 252, 0.1);
  border-radius: var(--radius-md);
  color: var(--md-purple);
  font-weight: 600;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1.2;
}

.experience-card:hover .company-logo-fallback {
  background-color: var(--md-purple);
  color: white;
  transform: rotate(10deg) scale(1.1);
  box-shadow: 0 0 15px rgba(187, 134, 252, 0.5);
}

.company-name {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-secondary);
}

/* New squared logo styles */
.company-info {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.company-logo-square {
  width: 70px;
  height: 70px;
  object-fit: contain;
  border-radius: var(--radius-md);
  background-color: rgba(255, 255, 255, 0.95);
  padding: 10px;
  transition: all 0.3s ease;
  border: 1px solid rgba(187, 134, 252, 0.1);
  flex-shrink: 0;
  filter: brightness(1) contrast(1.1);
}

.experience-card:hover .company-logo-square {
  transform: rotate(5deg) scale(1.15);
  box-shadow: 0 0 15px rgba(187, 134, 252, 0.5);
  background-color: rgba(255, 255, 255, 1);
  filter: brightness(1.2) contrast(1.2);
}

.company-logo-fallback-square {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: rgba(187, 134, 252, 0.1);
  border-radius: var(--radius-md);
  color: var(--md-purple);
  font-weight: 600;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1.2;
  flex-shrink: 0;
}

.experience-card:hover .company-logo-fallback-square {
  background-color: var(--md-purple);
  color: white;
  transform: rotate(10deg) scale(1.1);
  box-shadow: 0 0 15px rgba(187, 134, 252, 0.5);
}

.company-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.company-url {
  font-size: 0.9rem;
  color: var(--primary-500);
  text-decoration: none;
  transition: all 0.3s ease;
}

.company-url:hover {
  color: var(--primary-400);
  text-decoration: underline;
}

.location-text {
  color: var(--text-muted);
  font-size: 0.9rem;
  font-weight: 500;
}

.experience-technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

.tech-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background-color: rgba(3, 218, 198, 0.1);
  color: var(--md-teal);
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.tech-badge:hover {
  background-color: rgba(3, 218, 198, 0.2);
  transform: translateY(-2px);
}

.achievements-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.achievements-list {
  padding-left: 1.5rem;
  margin-bottom: 0;
}

.achievements-list li {
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
}

.achievements-list li:last-child {
  margin-bottom: 0;
}

/* Education Section */
.education-card {
  background-color: rgba(30, 30, 30, 0.6);
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  padding: 1.5rem;
}

.education-card:hover {
  transform: translateX(10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(187, 134, 252, 0.3);
}

.education-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.education-degree {
  font-size: 1.3rem;
  font-weight: 700;
  margin: 0;
  color: var(--md-teal);
}

.education-duration {
  font-size: 0.9rem;
  color: var(--text-tertiary);
  background-color: rgba(3, 218, 198, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
}

.education-institution {
  display: flex;
  align-items: center;
}

.institution-logo-container {
  margin-right: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 80px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 10px;
  border: 1px solid rgba(3, 218, 198, 0.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.institution-logo {
  height: auto;
  max-height: 70px;
  max-width: 110px;
  object-fit: contain;
  transition: all 0.3s ease;
}

.education-card:hover .institution-logo-container {
  background-color: rgba(3, 218, 198, 0.1);
  border-color: rgba(3, 218, 198, 0.3);
  box-shadow: 0 5px 15px rgba(3, 218, 198, 0.1);
}

.education-card:hover .institution-logo {
  transform: rotate(5deg) scale(1.1);
  box-shadow: 0 0 15px rgba(3, 218, 198, 0.5);
}

.institution-logo-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: rgba(3, 218, 198, 0.1);
  border-radius: var(--radius-md);
  color: var(--md-teal);
  font-weight: 600;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1.2;
}

.education-card:hover .institution-logo-fallback {
  background-color: var(--md-teal);
  color: white;
  transform: rotate(10deg) scale(1.1);
  box-shadow: 0 0 15px rgba(3, 218, 198, 0.5);
}

.institution-name {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-secondary);
}

/* New squared logo styles for education */
.institution-info {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.institution-logo-square {
  width: 70px;
  height: 70px;
  object-fit: contain;
  border-radius: var(--radius-md);
  background-color: rgba(255, 255, 255, 0.95);
  padding: 10px;
  transition: all 0.3s ease;
  border: 1px solid rgba(3, 218, 198, 0.1);
  flex-shrink: 0;
  filter: brightness(1) contrast(1.1);
}

.education-card:hover .institution-logo-square {
  transform: rotate(5deg) scale(1.15);
  box-shadow: 0 0 15px rgba(3, 218, 198, 0.5);
  background-color: rgba(255, 255, 255, 1);
  filter: brightness(1.2) contrast(1.2);
}

.institution-logo-fallback-square {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: rgba(3, 218, 198, 0.1);
  border-radius: var(--radius-md);
  color: var(--md-teal);
  font-weight: 600;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1.2;
  flex-shrink: 0;
}

.education-card:hover .institution-logo-fallback-square {
  background-color: var(--md-teal);
  color: white;
  transform: rotate(10deg) scale(1.1);
  box-shadow: 0 0 15px rgba(3, 218, 198, 0.5);
}

.institution-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.institution-url {
  font-size: 0.9rem;
  color: var(--primary-500);
  text-decoration: none;
  transition: all 0.3s ease;
}

.institution-url:hover {
  color: var(--primary-400);
  text-decoration: underline;
}

/* Certifications */
.certification-card {
  background-color: rgba(30, 30, 30, 0.6);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  height: 100%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.certification-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(187, 134, 252, 0.3);
}

.certification-logo {
  margin-bottom: 1rem;
}

.cert-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 8px;
  background-color: rgba(187, 134, 252, 0.1);
  color: var(--md-purple);
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.certification-card:hover .cert-icon {
  background-color: var(--md-purple);
  color: white;
  transform: rotate(10deg) scale(1.1);
  box-shadow: 0 0 15px rgba(187, 134, 252, 0.5);
}

.certification-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.certification-issuer {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.certification-date {
  font-size: 0.8rem;
  color: var(--text-tertiary);
  margin-bottom: 1rem;
}

.certification-verification {
  margin-top: 0.5rem;
}

.verification-link {
  display: inline-block;
  padding: 0.4rem 1rem;
  background-color: rgba(3, 218, 198, 0.1);
  color: var(--md-teal);
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid rgba(3, 218, 198, 0.2);
}

.verification-link:hover {
  background-color: rgba(3, 218, 198, 0.2);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(3, 218, 198, 0.2);
}

/* Timeline */
.timeline {
  position: relative;
  padding-left: 1.5rem;
}

.timeline .card {
  position: relative;
  border-radius: var(--radius-lg);
  background-color: var(--bg-primary);
  transition: all 0.3s ease;
}

.timeline .card:hover {
  transform: translateX(10px);
  box-shadow: var(--shadow-md);
}

.timeline .card::before {
  content: '';
  position: absolute;
  left: -1.5rem;
  top: 1.5rem;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(0, 118, 255, 0.2);
}

.timeline::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 2px;
  height: 100%;
  background-color: var(--border-color);
}

@media (max-width: 992px) {
  .about-content {
    padding-right: 0;
    margin-bottom: 2rem;
    height: auto;
  }

  .about-card {
    margin-top: 1rem;
  }

  .experience-header, .education-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .experience-duration, .education-duration {
    margin-top: 0.5rem;
  }

  .section.bg-gradient-dark.reduced-gap {
    margin-top: 1rem !important;
    padding-top: 1.5rem !important;
  }
}

@media (max-width: 768px) {
  .about-stats {
    flex-direction: column;
  }

  .stat-item {
    margin: 0.5rem 0;
  }

  .stat-item:first-child {
    margin-top: 0;
  }

  .stat-item:last-child {
    margin-bottom: 0;
  }

  .experience-technologies, .tech-stack-icons {
    justify-content: flex-start;
  }

  .experience-card, .education-card {
    padding: 1.25rem;
  }

  .experience-position, .education-degree {
    font-size: 1.25rem;
  }

  .certification-card {
    margin-bottom: 1rem;
  }
}
