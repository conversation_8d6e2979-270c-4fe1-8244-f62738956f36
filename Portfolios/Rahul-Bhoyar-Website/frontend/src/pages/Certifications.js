import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Spinner } from 'react-bootstrap';
import axios from 'axios';
import CertificationCard from '../components/CertificationCard';
import './Certifications.css';

const Certifications = () => {
  const [certifications, setCertifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCertifications = async () => {
      try {
        const response = await axios.get('/api/certifications');
        setCertifications(response.data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching certifications:', err);
        setError('Failed to load certifications. Please try again later.');
        setLoading(false);
      }
    };

    fetchCertifications();
  }, []);

  if (loading) {
    return (
      <Container className="py-5 mt-5">
        <div className="text-center">
          <Spinner animation="border" variant="primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p className="mt-3">Loading certifications...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-5 mt-5">
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </Container>
    );
  }

  return (
    <section className="certifications-section">
      <Container>
        <h2 className="section-title text-center">Professional Certifications</h2>
        <div className="section-intro text-center mb-5">
          <p className="lead">
            Continuous professional development and industry-recognized credentials
          </p>
        </div>

        <Row>
          {certifications.map((certification) => (
            <Col md={6} lg={4} key={certification.id} className="mb-4">
              <CertificationCard certification={certification} />
            </Col>
          ))}
        </Row>

        {certifications.length === 0 && !loading && (
          <div className="no-certifications text-center py-5">
            <h3>No certifications available</h3>
            <p>Check back later for updates.</p>
          </div>
        )}
      </Container>
    </section>
  );
};

export default Certifications;
