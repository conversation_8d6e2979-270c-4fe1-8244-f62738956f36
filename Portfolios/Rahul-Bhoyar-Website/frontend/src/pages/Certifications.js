import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Button, InputGroup, FormControl, Spinner } from 'react-bootstrap';
import { FaSearch, FaFilter, FaSortAmountDown, FaSortAmountUp } from 'react-icons/fa';
import axios from 'axios';
import CertificationCard from '../components/CertificationCard';
import './Certifications.css';

const Certifications = () => {
  const [certifications, setCertifications] = useState([]);
  const [filteredCertifications, setFilteredCertifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortOrder, setSortOrder] = useState('desc'); // 'desc' for newest first

  // Sample categories - in a real app, these would be derived from the certifications data
  const categories = ['All', 'Cloud Computing', 'AI/ML', 'Data Science', 'Cybersecurity', 'Programming', 'Database', 'Professional', 'Education'];

  useEffect(() => {
    const fetchCertifications = async () => {
      try {
        const response = await axios.get('/api/certifications');
        const certificationsData = response.data;

        setCertifications(certificationsData);
        setFilteredCertifications(certificationsData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching certifications:', err);
        setError('Failed to load certifications. Please try again later.');
        setLoading(false);
      }
    };

    fetchCertifications();
  }, []);

  // Filter and sort certifications when search term, category, or sort order changes
  useEffect(() => {
    let result = [...certifications];

    // Filter by search term
    if (searchTerm) {
      result = result.filter(cert =>
        cert.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cert.issuer.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (cert.description && cert.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (cert.skills && cert.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase())))
      );
    }

    // Filter by category
    if (selectedCategory !== 'All') {
      result = result.filter(cert => cert.category === selectedCategory);
    }

    // Sort by date
    result = result.sort((a, b) => {
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      return sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
    });

    setFilteredCertifications(result);
  }, [searchTerm, selectedCategory, sortOrder, certifications]);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
  };

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc');
  };

  if (loading) {
    return (
      <Container className="py-5 mt-5">
        <div className="text-center">
          <Spinner animation="border" variant="primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p className="mt-3">Loading certifications...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-5 mt-5">
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </Container>
    );
  }

  return (
    <section className="certifications-section">
      <Container>
        <h2 className="section-title text-center">Professional Certifications</h2>
        <div className="section-intro text-center mb-5">
          <p className="lead">
            Continuous professional development and industry-recognized credentials
          </p>
        </div>

        <div className="certifications-filters mb-5">
          <Row className="align-items-center">
            <Col lg={6} md={12} className="mb-3 mb-lg-0">
              <InputGroup>
                <InputGroup.Text>
                  <FaSearch />
                </InputGroup.Text>
                <FormControl
                  placeholder="Search certifications..."
                  value={searchTerm}
                  onChange={handleSearch}
                />
              </InputGroup>
            </Col>
            <Col lg={6} md={12} className="d-flex justify-content-lg-end">
              <div className="category-filters">
                {categories.map((category, index) => (
                  <Button
                    key={index}
                    variant={selectedCategory === category ? 'primary' : 'outline-primary'}
                    className="category-filter-btn me-2 mb-2"
                    onClick={() => handleCategoryChange(category)}
                  >
                    {category}
                  </Button>
                ))}
                <Button
                  variant="outline-secondary"
                  className="sort-btn ms-2"
                  onClick={toggleSortOrder}
                  title={sortOrder === 'desc' ? 'Newest first' : 'Oldest first'}
                >
                  {sortOrder === 'desc' ? <FaSortAmountDown /> : <FaSortAmountUp />}
                </Button>
              </div>
            </Col>
          </Row>
        </div>

        <div className="certifications-count mb-4 text-left">
          <p>{filteredCertifications.length} certifications found</p>
        </div>

        <Row>
          {filteredCertifications.map((certification) => (
            <Col md={6} lg={4} key={certification.id} className="mb-4">
              <CertificationCard certification={certification} />
            </Col>
          ))}
        </Row>

        {filteredCertifications.length === 0 && (
          <div className="no-certifications text-center py-5">
            <h3>No certifications found</h3>
            <p>Try adjusting your search or filter criteria.</p>
          </div>
        )}
      </Container>
    </section>
  );
};

export default Certifications;
