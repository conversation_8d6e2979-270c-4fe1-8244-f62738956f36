/* Certifications Page Styles */
.certifications-section {
  padding-top: 120px;
  padding-bottom: 80px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--md-purple), var(--md-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-intro {
  max-width: 600px;
  margin: 0 auto;
}

.section-intro .lead {
  font-size: 1.2rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Certifications Filters */
.certifications-filters {
  background-color: rgba(30, 30, 30, 0.6);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
}

.certifications-filters .input-group {
  background-color: rgba(18, 18, 18, 0.4);
  border-radius: 8px;
  overflow: hidden;
}

.certifications-filters .input-group-text {
  background-color: transparent;
  border: none;
  color: var(--md-purple);
}

.certifications-filters .form-control {
  background-color: transparent;
  border: none;
  color: var(--text-primary);
  padding: 0.75rem 1rem;
  font-size: 1rem;
}

.certifications-filters .form-control:focus {
  box-shadow: none;
  background-color: rgba(18, 18, 18, 0.6);
}

.certifications-filters .form-control::placeholder {
  color: var(--text-tertiary);
}

/* Category Filters */
.category-filters {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.category-filter-btn {
  border-radius: 50px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.category-filter-btn.btn-primary {
  background: linear-gradient(90deg, var(--md-purple), var(--md-purple-variant));
  border-color: transparent;
  box-shadow: 0 4px 10px rgba(187, 134, 252, 0.2);
}

.category-filter-btn.btn-outline-primary {
  color: var(--md-purple);
  border-color: rgba(187, 134, 252, 0.3);
}

.category-filter-btn.btn-outline-primary:hover {
  background-color: rgba(187, 134, 252, 0.1);
  color: var(--md-purple);
  border-color: var(--md-purple);
}

.sort-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  color: var(--md-teal);
  border-color: rgba(3, 218, 198, 0.3);
  transition: all 0.3s ease;
}

.sort-btn:hover {
  background-color: rgba(3, 218, 198, 0.1);
  color: var(--md-teal);
  border-color: var(--md-teal);
}

/* Certifications Count */
.certifications-count {
  color: var(--text-secondary);
  font-size: 0.95rem;
  font-weight: 500;
}

/* No Certifications */
.no-certifications {
  background-color: rgba(30, 30, 30, 0.6);
  border-radius: 16px;
  padding: 3rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(187, 134, 252, 0.1);
  backdrop-filter: blur(10px);
}

.no-certifications h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.no-certifications p {
  color: var(--text-secondary);
}

/* Responsive */
@media (max-width: 992px) {
  .certifications-section {
    padding-top: 100px;
  }

  .category-filters {
    justify-content: flex-start;
    margin-top: 1rem;
  }
}

@media (max-width: 768px) {
  .certifications-section {
    padding-top: 80px;
  }

  .certifications-filters {
    padding: 1rem;
  }
}

.verification-link:hover {
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(187, 134, 252, 0.4);
}

.verification-unavailable {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: rgba(128, 128, 128, 0.1);
  color: var(--text-tertiary);
  border-radius: var(--radius-sm);
  font-size: 0.85rem;
  font-weight: 500;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .section {
    padding: 2rem 0;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-intro .lead {
    font-size: 1.1rem;
  }

  .certification-card {
    margin-bottom: 1rem;
  }

  .cert-logo-img {
    width: 50px;
    height: 50px;
    padding: 6px;
  }

  .cert-icon-fallback {
    width: 50px;
    height: 50px;
    font-size: 0.7rem;
  }

  .certification-title {
    font-size: 1rem;
  }
}
