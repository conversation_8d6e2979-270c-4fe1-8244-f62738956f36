import React, { useState, useEffect } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import axios from 'axios';
import SkillBar from '../components/SkillBar';
import './About.css';

// Helper function to get technology logo
const getTechLogo = (techName) => {
  const techLogos = {
    'LangGraph': 'Langgraph.png',
    'LangChain': 'Langchain.png',
    'Phidata': 'default-tech.svg',
    'LlamaIndex': 'default-tech.svg',
    'CrewAI': 'default-tech.svg',
    'TensorFlow': 'Tensorflow_logo.png',
    'PyTorch': 'pytorch_logo.png',
    'Python': 'python.png',
    'FastAPI': 'FastAPI_logo.png',
    'Django': 'Django.png',
    'Flask': 'flask.png',
    'React.js': 'React.png',
    'React': 'React.png',
    'JavaScript': 'JavaScript.png',
    'TypeScript': 'JavaScript.png',
    'HTML5': 'default-tech.svg',
    'CSS3': 'default-tech.svg',
    'Jupyter': 'Jupyter_logo.png',
    'HuggingFace': 'Huggingface_logo.svg',
    'Predictive Modeling': 'default-tech.svg',
    'Natural Language Processing': 'default-tech.svg',
    'Computer Vision': 'default-tech.svg',
    'MLOps & Model Deployment': 'default-tech.svg',
    'RESTful API Design': 'default-tech.svg',
    'Database Design': 'default-tech.svg',
    'Authentication & Security': 'default-tech.svg',
    'Microservices Architecture': 'default-tech.svg',
    'Redux': 'React.png',
    'Responsive Design': 'default-tech.svg',
    'UI/UX Principles': 'default-tech.svg'
  };

  // Check for exact match first
  if (techLogos[techName]) {
    return `/images/about/skills/${techLogos[techName]}`;
  }

  // Check for partial matches
  const lowerTechName = techName.toLowerCase();
  for (const [key, value] of Object.entries(techLogos)) {
    if (lowerTechName.includes(key.toLowerCase()) || key.toLowerCase().includes(lowerTechName)) {
      return `/images/about/skills/${value}`;
    }
  }

  // Default icon for unknown technologies
  return `/images/about/skills/default-tech.svg`;
};

// Component for skill list item with logo
const SkillListItem = ({ children }) => {
  const techName = children;
  const logoSrc = getTechLogo(techName);

  return (
    <li className="skill-list-item-with-logo">
      <img
        src={`${process.env.PUBLIC_URL}${logoSrc}`}
        alt={`${techName} logo`}
        className="skill-logo"
        onError={(e) => {
          e.target.src = `${process.env.PUBLIC_URL}/images/about/skills/default-tech.svg`;
        }}
      />
      <span>{children}</span>
    </li>
  );
};

const About = () => {
  const [skills, setSkills] = useState([]);
  const [experience, setExperience] = useState([]);
  const [education, setEducation] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Fetch data from API
    const fetchData = async () => {
      try {
        const [skillsRes, expRes, eduRes] = await Promise.all([
          axios.get('/api/skills'),
          axios.get('/api/experience'),
          axios.get('/api/education')
        ]);

        setSkills(skillsRes.data);
        setExperience(expRes.data);
        setEducation(eduRes.data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <Container className="py-5 mt-5">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading about information...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-5 mt-5">
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </Container>
    );
  }

  return (
    <div className="mt-5 pt-4">
      <section className="section">
        <Container>
          <h2 className="section-title text-center mb-5">About Me</h2>
          <Row className="align-items-start mb-4">
            <Col lg={6} className="mb-4 mb-lg-0">
              <div className="about-content text-left">
                <h3 className="mb-3 text-gradient">AI and Software Professional</h3>
                <p className="lead">
                  I am an AI and Software Professional with over seven years of expertise in GenAI, AgenticAI, NLP, Deep Learning, Machine Learning, Data Science, and Web and APIs Development.
                </p>
                <p>
                  Currently, I work as an Research Associate/AI Engineer at the DFKI, Berlin (German Research Centre for Artificial Intelligence), actively contributing to transformative projects in education, research, e- commerce and healthcare for leading organizations.
                </p>
                <p>
                  My technical proficiency encompasses a wide range of tools and technologies, including Python, SQL, Django, Flask, FastAPI, GenAI and Agentic AI frameworks like LangChain, LangGraph, LlamaIndex, and cloud platforms such as AWS, GCP, and Microsoft Azure. Additionally, I am skilled in front-end technologies like HTML, CSS, and JavaScript, React, as well as Data Engineering and System Architecture.
                </p>

                <p>
                  Throughout my career, I have held diverse roles, including Python Full Stack Developer, Data Scientist/Engineer, Generative AI Engineer, and ML/AI Engineer. I bring strong expertise in System Design, Microservices, and both Agile (Scrum) and Waterfall business models. With a commitment to industry-standard programming practices, I consistently strive to stay at the forefront of technological advancements in software development.
                </p>
                <div className="about-stats">
                  <div className="stat-item">
                    <span className="stat-number counter">7+</span>
                    <span className="stat-label">Years Experience</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-number counter">75+</span>
                    <span className="stat-label">Projects Delivered</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-number counter">20+</span>
                    <span className="stat-label">Tech Stack Mastery</span>
                  </div>
                </div>
              </div>
            </Col>
            <Col lg={6}>
              <div className="about-card">
                <div className="about-card-header">
                  <div className="about-card-avatar-container">
                    <img
                      src={`${process.env.PUBLIC_URL}/images/about/profile/Rahul_Bhoyar_passport_photo.jpg`}
                      alt="Rahul Bhoyar"
                      className="about-card-avatar-image"
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'flex';
                      }}
                    />
                    <div className="about-card-avatar" style={{display: 'none'}}>RB</div>
                  </div>
                  <div className="about-card-info">
                    <h3>Rahul Bhoyar</h3>
                    <p>AI and Software Professional</p>
                  </div>
                </div>
                <div className="about-card-body">
                  <div className="about-card-section text-left">
                    <h4>Core Technologies</h4>
                    <div className="tech-stack-icons">
                      <span className="tech-icon" title="Python">Py</span>
                      <span className="tech-icon" title="JavaScript">Js</span>
                      <span className="tech-icon" title="React">Re</span>
                      <span className="tech-icon" title="FastAPI">Fa</span>
                      <span className="tech-icon" title="Docker">Do</span>
                      <span className="tech-icon" title="TensorFlow">Tf</span>
                      <span className="tech-icon" title="PyTorch">Pt</span>
                      <span className="tech-icon" title="AWS">Aw</span>
                    </div>
                  </div>
                  <div className="about-card-section text-left">
                    <h4>Specializations</h4>
                    <p>Agentic AI, Generative AI, Deep Learning, Machine Learning, Data Science, Data Engineering, Backend and Frontend Development</p>
                  </div>
                  <div className="about-card-section text-left">
                    <h4>Languages</h4>
                    <p>English (C2), German (A2)</p>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      <section className="section bg-gradient-dark reduced-gap">
        <Container>
          <h2 className="section-title text-center">Technical Skills</h2>
          <div className="skills-intro text-center mb-5">
            <p className="lead">
              My expertise spans across multiple domains, with a focus on full-stack development and AI technologies
            </p>
          </div>
          <Row>
            {skills.map((skill, index) => (
              <Col md={6} key={index} className="mb-4">
                <SkillBar skill={skill} />
              </Col>
            ))}
          </Row>
          <div className="skills-categories mt-5">
            <Row>
              <Col md={6} className="mb-4">
                <div className="skill-category-card text-left">
                  <h4 className="text-accent mb-3">🤖 Agentic AI and Generative AI</h4>
                  <ul className="skill-list">
                    <SkillListItem>LangGraph</SkillListItem>
                    <SkillListItem>LangChain</SkillListItem>
                    <SkillListItem>Phidata</SkillListItem>
                    <SkillListItem>LlamaIndex</SkillListItem>
                    <SkillListItem>CrewAI</SkillListItem>
                  </ul>
                </div>
              </Col>
              <Col md={6} className="mb-4">
                <div className="skill-category-card text-left">
                  <h4 className="text-accent mb-3">🧠 Deep Learning and Machine Learning</h4>
                  <ul className="skill-list">
                    <SkillListItem>Predictive Modeling</SkillListItem>
                    <SkillListItem>Natural Language Processing</SkillListItem>
                    <SkillListItem>Computer Vision</SkillListItem>
                    <SkillListItem>TensorFlow & PyTorch</SkillListItem>
                    <SkillListItem>MLOps & Model Deployment</SkillListItem>
                  </ul>
                </div>
              </Col>
              <Col md={6} className="mb-4">
                <div className="skill-category-card text-left">
                  <h4 className="text-secondary mb-3">⚙️ Backend Development</h4>
                  <ul className="skill-list">
                    <SkillListItem>Python & frameworks (FastAPI, Django, Flask)</SkillListItem>
                    <SkillListItem>RESTful API Design</SkillListItem>
                    <SkillListItem>Database Design</SkillListItem>
                    <SkillListItem>Authentication & Security</SkillListItem>
                    <SkillListItem>Microservices Architecture</SkillListItem>
                  </ul>
                </div>
              </Col>
              <Col md={6} className="mb-4">
                <div className="skill-category-card text-left">
                  <h4 className="text-primary mb-3">🎨 Frontend Development</h4>
                  <ul className="skill-list">
                    <SkillListItem>React.js & Redux</SkillListItem>
                    <SkillListItem>JavaScript/TypeScript</SkillListItem>
                    <SkillListItem>HTML5 & CSS3</SkillListItem>
                    <SkillListItem>Responsive Design</SkillListItem>
                    <SkillListItem>UI/UX Principles</SkillListItem>
                  </ul>
                </div>
              </Col>
            </Row>
          </div>
        </Container>
      </section>

      <section className="section">
        <Container>
          <h2 className="section-title text-center">Professional Experience</h2>
          <div className="section-intro text-center mb-5">
            <p className="lead">
              A track record of delivering impactful solutions across various industries
            </p>
          </div>
          <Row>
            <Col lg={8} className="mx-auto">
              <div className="timeline">
                {experience.map((exp, index) => (
                  <div className="experience-card mb-4" key={index}>
                    <div className="card-body text-left">
                      <div className="experience-header">
                        <h3 className="experience-position">{exp.position}</h3>
                        <div className="experience-duration">
                          {exp.start_date} - {exp.end_date || 'Present'}
                          {exp.location && (
                            <span className="location-text"> • {exp.location}</span>
                          )}
                        </div>
                      </div>
                      <div className="experience-company mb-3">
                        <div className="company-info">
                          {exp.logo && (
                            <img
                              src={exp.logo}
                              alt={`${exp.company} logo`}
                              className="company-logo-square"
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextSibling.style.display = 'block';
                              }}
                            />
                          )}
                          <span className="company-logo-fallback-square" style={{display: exp.logo ? 'none' : 'block'}}>
                            {exp.company.charAt(0).toUpperCase()}
                          </span>
                          <div className="company-details">
                            <span className="company-name">{exp.company}</span>
                            {exp.company_url && (
                              <a
                                href={exp.company_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="company-url"
                              >
                                {exp.company_url}
                              </a>
                            )}
                          </div>
                        </div>
                      </div>
                      <p className="experience-description">{exp.description}</p>
                      <div className="experience-technologies">
                        {exp.technologies.map((tech, i) => (
                          <span className="tech-badge" key={i}>
                            {tech}
                          </span>
                        ))}
                      </div>
                      <div className="experience-achievements mt-3">
                        <h5 className="achievements-title">Key Achievements:</h5>
                        <ul className="achievements-list">
                          {exp.achievements && exp.achievements.map((achievement, i) => (
                            <li key={i}>{achievement}</li>
                          ))}
                          {!exp.achievements && (
                            <>
                              <li>Successfully delivered projects on time and within budget</li>
                              <li>Collaborated effectively with cross-functional teams</li>
                              <li>Implemented best practices and improved development workflows</li>
                            </>
                          )}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      <section className="section bg-gradient-dark">
        <Container>
          <h2 className="section-title text-center">Education</h2>
          <div className="section-intro text-center mb-5">
            <p className="lead">
              Academic background and formal education
            </p>
          </div>
          <Row>
            <Col lg={8} className="mx-auto">
              <div className="timeline">
                {education.map((edu, index) => (
                  <div className="education-card mb-4" key={index}>
                    <div className="card-body text-left">
                      <div className="education-header">
                        <h3 className="education-degree">{edu.degree} in {edu.field}</h3>
                        <div className="education-duration">
                          {edu.start_date} - {edu.end_date}
                          {edu.location && (
                            <span className="location-text"> • {edu.location}</span>
                          )}
                        </div>
                      </div>
                      <div className="education-institution mb-3">
                        <div className="institution-info">
                          {edu.logo && (
                            <img
                              src={edu.logo}
                              alt={`${edu.institution} logo`}
                              className="institution-logo-square"
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextSibling.style.display = 'block';
                              }}
                            />
                          )}
                          <span className="institution-logo-fallback-square" style={{display: edu.logo ? 'none' : 'block'}}>
                            {edu.institution.charAt(0).toUpperCase()}
                          </span>
                          <div className="institution-details">
                            <span className="institution-name">{edu.institution}</span>
                            {edu.institution_url && (
                              <a
                                href={edu.institution_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="institution-url"
                              >
                                {edu.institution_url}
                              </a>
                            )}
                          </div>
                        </div>
                      </div>
                      {edu.description && <p className="education-description">{edu.description}</p>}
                      {edu.achievements && (
                        <div className="education-achievements">
                          <h5 className="achievements-title">Highlights:</h5>
                          <ul className="achievements-list">
                            {edu.achievements.map((achievement, i) => (
                              <li key={i}>{achievement}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </Col>
          </Row>


        </Container>
      </section>
    </div>
  );
};

export default About;
